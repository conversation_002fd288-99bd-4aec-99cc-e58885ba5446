import 'package:flutter/material.dart';

class AppColors {
  // Primary
  static const Color primary = Color(0xFF0A84FF);     // Xanh dương chủ đạo
  static const Color primaryDark = Color(0xFF0060DF);
  static const Color primaryLight = Color(0xFF5AC8FA);

  // Background
  static const Color background = Color(0xFFF9F9F9);
  static const Color scaffold = Color(0xFFFFFFFF);

  // Text
  static const Color textPrimary = Color(0xFF1C1C1E);
  static const Color textSecondary = Color(0xff3c3c4399);
  static const Color textDisabled = Color(0xFF8E8E93);

  // Success / Error
  static const Color success = Color(0xFF34C759);
  static const Color error = Color(0xFFFF3B30);
  static const Color warning = Color(0xFFFF9500);
  static const Color info = Color(0xFF5AC8FA);

  // Border / Shadow
  static const Color border = Color(0xFFE5E5EA);
  static const Color shadow = Color(0x1A000000); // 10% black

  // Button
  static const Color button = primary;
  static const Color buttonText = scaffold;

  // Custom example
  static const Color card = Color(0xFFF2F2F7);

  
}
