import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';


class AppFont {
  static final heading = GoogleFonts.inter(
    fontSize: 28,
    fontWeight: FontWeight.bold,
  );

  static final title = GoogleFonts.inter(
    fontSize: 22,
    fontWeight: FontWeight.w600,
  );
  static final subtitle = GoogleFonts.inter(
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static final body = GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.normal,
  );
  

  static final caption = GoogleFonts.inter(
    fontSize: 14,
    color: Colors.grey,
  );

  static final button = GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );
}
