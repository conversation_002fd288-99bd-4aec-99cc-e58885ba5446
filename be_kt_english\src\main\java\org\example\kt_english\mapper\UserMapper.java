package org.example.kt_english.mapper;


import org.example.kt_english.dto.request.user.UserCreationRequest;
import org.example.kt_english.dto.response.user.UserResponse;
import org.example.kt_english.entity.User;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface UserMapper  {
    User toUser(UserCreationRequest request);
    UserResponse toUserResponse(User user);
//    void updateUser(@MappingTarget User user, UserUpdateRequest request);
}
