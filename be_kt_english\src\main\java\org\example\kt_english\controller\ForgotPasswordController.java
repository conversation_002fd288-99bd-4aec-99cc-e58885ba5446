package org.example.kt_english.controller;

import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.example.kt_english.dto.request.user.ChangePasswordRequest;
import org.example.kt_english.dto.request.user.ForgotPasswordRequest;
import org.example.kt_english.dto.request.user.VerifyOtpRequest;
import org.example.kt_english.dto.response.ApiResponse;
import org.example.kt_english.service.ForgotPasswordService;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/forgot-password")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ForgotPasswordController {
    ForgotPasswordService forgotPasswordService;

    @PostMapping("/verify-email")
    public ApiResponse<String> verifyEmail(@Valid @RequestBody ForgotPasswordRequest request) {
        return forgotPasswordService.verifyEmail(request);
    }

    @PostMapping("/verify-otp")
    public ApiResponse<String> verifyOtp(@Valid @RequestBody VerifyOtpRequest request) {
        return forgotPasswordService.verifyOtp(request);
    }

    @PostMapping("/change-password")
    public ApiResponse<String> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        return forgotPasswordService.changePassword(request);
    }
}