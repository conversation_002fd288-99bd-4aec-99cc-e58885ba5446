{"inputs": ["E:\\Project\\KT_ENGLISH\\fe_kt_english\\.dart_tool\\flutter_build\\98c707ecc9ecd91cf3a16ee45617ba5f\\app.dill", "E:\\Download\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\pubspec.yaml", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "E:\\Download\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "E:\\Download\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\.dart_tool\\flutter_build\\98c707ecc9ecd91cf3a16ee45617ba5f\\native_assets.json", "E:\\Download\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "E:\\Download\\flutter\\packages\\flutter\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dio-5.8.0+1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dio_web_adapter-2.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\get-4.7.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\google_fonts-6.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD689439776"], "outputs": ["E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "E:\\Project\\KT_ENGLISH\\fe_kt_english\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}