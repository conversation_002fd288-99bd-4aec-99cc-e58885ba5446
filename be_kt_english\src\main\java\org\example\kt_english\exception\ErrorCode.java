package org.example.kt_english.exception;

import lombok.Getter;


@Getter
public enum ErrorCode {
    UNCATEGORIZED_EXCEPTION(9999, "Uncategorized error"),
    INVALID_KEY(1001, "Uncategorized error"),
    USER_EXISTED(1002, "User existed"),
    USERNAME_INVALID(1003, "Username must be at least 10 characters"),
    INVALID_PASSWORD(1004, "Password must be at least 10 characters"),
    USER_NOT_EXISTED(1005, "User not existed"),
    UNAUTHENTICATED(1006, "Unauthenticated"),
    NOT_BLANK(1007, "Not blank"),
    VALIDATION_EMAIL(1008, "Please enter a valid email address"),
    PASSWORD_INVALID(1009, "Password must contain at least one lowercase letter, one uppercase letter, and one number."),
    EMAIL_NOT_FOUND(1010, "Email not found"),
    CHECK_LOGIN_FAILED(1011, "Email or password is incorrect"),
    VALIDATE_OTP_FAILED(1012, "O<PERSON> must be at 6 characters"),
    OTP_EXPIRED(1013, "<PERSON><PERSON> has expired"),
    INVALID_OTP(1014, "Invalid OTP"),
    PASSWORD_MISMATCH(1015, "Passwords do not match");
    ;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    private final int code;
    private final String message;

}