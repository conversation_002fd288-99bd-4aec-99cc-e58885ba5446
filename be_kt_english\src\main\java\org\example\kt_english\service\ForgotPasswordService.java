package org.example.kt_english.service;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.example.kt_english.dto.request.user.ChangePasswordRequest;
import org.example.kt_english.dto.request.user.ForgotPasswordRequest;
import org.example.kt_english.dto.request.user.VerifyOtpRequest;
import org.example.kt_english.dto.response.ApiResponse;
import org.example.kt_english.dto.response.user.MailBody;
import org.example.kt_english.entity.ForgotPassword;
import org.example.kt_english.entity.User;
import org.example.kt_english.exception.AppException;
import org.example.kt_english.exception.ErrorCode;
import org.example.kt_english.repository.ForgotPasswordRepository;
import org.example.kt_english.repository.UserRepository;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Random;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ForgotPasswordService {

    UserRepository userRepository;
    ForgotPasswordRepository forgotPasswordRepository;
    EmailService emailService;


    private static final long OTP_EXPIRATION_MS = 300_000; // 5 minutes

    @Transactional
    public ApiResponse<String> verifyEmail(ForgotPasswordRequest request) {
        User user = userRepository.findByEmailIgnoreCase(request.getEmail())
                .orElseThrow(() -> new AppException(ErrorCode.EMAIL_NOT_FOUND));

        int otp = generateOtp();
        MailBody mailBody = MailBody.builder()
                .to(request.getEmail())
                .subject("OTP for Password Reset")
                .text("Your OTP for password reset is: " + otp + "\nValid for 5 minutes.")
                .build();

        ForgotPassword forgotPassword = ForgotPassword.builder()
                .otp(otp)
                .expirationTime(new Date(System.currentTimeMillis() + OTP_EXPIRATION_MS))
                .user(user)
                .build();

        // Delete any existing OTPs for this user
        forgotPasswordRepository.deleteByUser(user);
        forgotPasswordRepository.save(forgotPassword);
        emailService.sendSimpleMessage(mailBody);

        return ApiResponse.<String>builder()
                .code(200)
                .message("OTP sent to your email")
                .build();
    }

    @Transactional
    public ApiResponse<String> verifyOtp(VerifyOtpRequest request) {
        User user = userRepository.findByEmailIgnoreCase(request.getEmail())
                .orElseThrow(() -> new AppException(ErrorCode.EMAIL_NOT_FOUND));

        ForgotPassword forgotPassword = forgotPasswordRepository.findByOtpAndUser(request.getOtp(), user)
                .orElseThrow(() -> new AppException(ErrorCode.INVALID_OTP));

        if (forgotPassword.getExpirationTime().before(new Date())) {
            forgotPasswordRepository.delete(forgotPassword);
            throw new AppException(ErrorCode.OTP_EXPIRED);
        }

        return ApiResponse.<String>builder()
                .code(200)
                .message("OTP verified successfully")
                .build();
    }

    @Transactional
    public ApiResponse<String> changePassword(ChangePasswordRequest request) {
//        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
//            throw new AppException(ErrorCode.PASSWORD_MISMATCH);
//        }
        User user = userRepository.findByEmailIgnoreCase(request.getEmail())
                .orElseThrow(() -> new AppException(ErrorCode.EMAIL_NOT_FOUND));

        // Verify OTP exists (ensures OTP was verified)
        ForgotPassword forgotPassword = forgotPasswordRepository.findByUser(user)
                .orElseThrow(() -> new AppException(ErrorCode.INVALID_OTP));

        if (forgotPassword.getExpirationTime().before(new Date())) {
            forgotPasswordRepository.delete(forgotPassword);
            throw new AppException(ErrorCode.OTP_EXPIRED);
        }
        PasswordEncoder passwordEncoder = new BCryptPasswordEncoder(10);
        String encodedPassword = passwordEncoder.encode(request.getNewPassword());
        userRepository.updatePassword(request.getEmail(), encodedPassword);

        // Delete the OTP after successful password change
        forgotPasswordRepository.delete(forgotPassword);

        return ApiResponse.<String>builder()
                .code(200)
                .message("Password changed successfully")
                .build();
    }

    private Integer generateOtp() {
        Random random = new Random();
        return random.nextInt(100_000, 999_999);
    }
}